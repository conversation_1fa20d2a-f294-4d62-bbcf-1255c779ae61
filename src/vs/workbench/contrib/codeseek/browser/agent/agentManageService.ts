import { createDecorator, IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';
import { InstantiationType, registerSingleton } from '../../../../../platform/instantiation/common/extensions.js';
import { Disposable } from '../../../../../base/common/lifecycle.js';
import { ICodeseekLogger } from '../../common/codeseekLogService.js';
import { IChatThreadService } from '../chatThreadService.js';
import { userMessageOpts } from '../chatThreadType.js';
import { IPlanAgentService } from './planAgentService.js';
import { ITodolistService } from '../todolistService.js';
import { CodeAgentService } from './codeAgentService.js';

export type StepResult = {
	status: 'success' | 'failed' | 'blocked';
	notes?: string;
	error?: string;
}

export interface IAgentManageService {
	readonly _serviceBrand: undefined;

	agentLoop(containerId: string, threadId: string, userMessageOpts: userMessageOpts, userMessageWithSelection: string): void;
}

export const IAgentManageService = createDecorator<IAgentManageService>('codeseekAgentManageService');
export class AgentManageService extends Disposable implements IAgentManageService {
	readonly _serviceBrand: undefined;

	constructor(
		@ICodeseekLogger private readonly logger: ICodeseekLogger,
		@IChatThreadService private readonly chatThreadService: IChatThreadService,
		@ITodolistService private readonly todolistService: ITodolistService,
		@IPlanAgentService private readonly planAgentService: IPlanAgentService,
		@IInstantiationService private readonly instantiationService: IInstantiationService,
	) {
		super();
	}

	public async agentLoop(containerId: string, threadId: string, userMessageOpts: userMessageOpts, userMessageWithSelection: string) {
		let shouldSendAnotherMessage = true;
		let lastStepResult: StepResult | undefined;
		while (shouldSendAnotherMessage) {
			const results: Record<number, StepResult> = {};
			shouldSendAnotherMessage = false;
			await this.planAgentService.createPlan(userMessageOpts, lastStepResult);
			if (this.todolistService.getTodolist().length === 0) {
				break;
			}
			while (true) {
				const todolist = this.todolistService.getTodolist();
				if (todolist.length === 0 || this.todolistService.hasFailedOrBlockedSteps()) {
					break;
				}
				this.logger.info(`Ready steps: ${todolist.join(', ')}`);

				const promises = todolist.map(stepIndex => async (stepIndex: number): Promise<void> => {
					this.logger.info(`Starting execution of step ${stepIndex}`);
					const codeAgentService = this.instantiationService.createInstance(CodeAgentService);
					const act_result: StepResult = await codeAgentService.act(userMessageOpts, stepIndex);
					results[stepIndex] = act_result;
				});
				await Promise.all(promises);
			}

			lastStepResult = Object.keys(results).length > 0
				? results[Math.max(...Object.keys(results).map(Number))]
				: undefined;
			if (lastStepResult?.status === 'failed' || lastStepResult?.status === 'blocked') {
				shouldSendAnotherMessage = true;
			}
		}
		this.chatThreadService.finishStreamingTextMessage(containerId, threadId, 'All steps completed successfully');
	}
}

registerSingleton(IAgentManageService, AgentManageService, InstantiationType.Eager);
