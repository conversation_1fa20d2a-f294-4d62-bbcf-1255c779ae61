import { createDecorator } from '../../../../../platform/instantiation/common/instantiation.js';
import { Disposable } from '../../../../../base/common/lifecycle.js';
import { ITodolistService } from '../todolistService.js';
import { IChatThreadService } from '../chatThreadService.js';
import { IToolsService } from '../../common/toolsService.js';
import { AskMessage, PluginMessageOpts, userMessageOpts } from '../chatThreadType.js';
import { StepResult } from './agentManageService.js';
import { ApproveRequestResultType, AskReponseType, ToolCallReturnType, ToolName, ToolNameEnum } from '../../common/toolsServiceTypes.js';
import pWaitFor from '../../../../common/pWaitFor.js';
import { ICodeseekLogger } from '../../common/codeseekLogService.js';
import { IPluginTaskService } from '../pluginTaskService.js';
import { ToolCallResultCode, ToolCallResultType, ToolCallType } from '../../common/llmMessageTypes.js';

export interface ICodeAgentService {
	readonly _serviceBrand: undefined;

	act(userMessageOpts: userMessageOpts, stepIndex: number): Promise<StepResult>;
}

export const ICodeAgentService = createDecorator<ICodeAgentService>('codeAgentService');
export class CodeAgentService extends Disposable implements ICodeAgentService {
	readonly _serviceBrand: undefined;

	constructor(
		@ICodeseekLogger private readonly logger: ICodeseekLogger,
		@IChatThreadService private readonly chatThreadService: IChatThreadService,
		@IToolsService private readonly toolsService: IToolsService,
		@ITodolistService private readonly todolistService: ITodolistService,
		@IPluginTaskService private readonly pluginTaskService: IPluginTaskService,
	) {
		super();
	}

	public async act(userMessageOpts: userMessageOpts, stepIndex: number): Promise<StepResult> {
		const step = this.todolistService.getTodolistEmitters();
		this.logger.info(`Starting execution of step ${step?.steps[stepIndex]}`);
		return {
			status: 'success',
			notes: `Step ${stepIndex} completed successfully`,
		};
	}

	async executeTool(containerId: string, threadId: string, toolCall: ToolCallType, userMessageOpts: userMessageOpts): Promise<ToolCallResultType> {
		const toolName = toolCall.name as ToolName;
		let toolResultVal: ToolCallReturnType[ToolName];
		let content: string = '';
		const ideTool = this.toolsService.toolFns[toolName];

		const onWait = async () => {
			this.chatThreadService.setStreamState(containerId, threadId, { isStreaming: false });
			const askMessage: AskMessage = {
				type: 'tool',
				content: toolCall,
			};
			this.chatThreadService.getCurrentThread(containerId).state.askMessage = askMessage;
			await pWaitFor(() => this.chatThreadService.containerState.allContainers[containerId].threadsState.allThreads[threadId].state.askResponse !== undefined, { interval: 100 });
			const currentThreadState = this.chatThreadService.containerState.allContainers[containerId].threadsState.allThreads[threadId].state;
			const response = { type: currentThreadState.askResponse!.type, response: currentThreadState.askResponse?.response, text: currentThreadState.askResponse?.text };
			currentThreadState.askResponse = undefined;
			currentThreadState.askResponseText = undefined;
			return response;
		};
		if (ideTool === undefined) {
			let externalToolsNeedApprove = true;
			const filteredTools = (userMessageOpts as PluginMessageOpts).taskInfo?.externalTools?.filter(tool => tool.toolName === toolName);
			if (filteredTools && filteredTools.length > 0) {
				externalToolsNeedApprove = filteredTools[0].needApprove;
			}
			if (externalToolsNeedApprove) {
				toolResultVal = await this.toolsService.toolFns[ToolNameEnum.APPROVE_REQUEST](toolCall.params as any, onWait);
				const approveResult = toolResultVal as ApproveRequestResultType;
				if (approveResult.response === AskReponseType.yesButtonClicked && userMessageOpts.from === 'Plugin') {
					this.pluginTaskService.fireToolCall((userMessageOpts as PluginMessageOpts).taskInfo.taskId, toolName, toolCall.params);
				}
				return { code: ToolCallResultCode.success, name: toolName, result: toolResultVal, error: '', content };
			}
			this.chatThreadService.setStreamState(containerId, threadId, { isStreaming: true });
			if (userMessageOpts.from === 'Plugin') {
				this.pluginTaskService.fireToolCall((userMessageOpts as PluginMessageOpts).taskInfo.taskId, toolName, toolCall.params);
				return { code: ToolCallResultCode.success, name: toolName, result: true, error: '', content };
			}
		}

		try {
			if (this.toolsService.isNeedApprove(toolName)) {
				toolResultVal = await this.toolsService.toolFns[ToolNameEnum.APPROVE_REQUEST](toolCall.params as any, onWait);
				content = this.toolsService.toolResultToString[toolName](toolResultVal as any);
				const approveResult = toolResultVal as ApproveRequestResultType;
				if (approveResult.response === AskReponseType.noButtonClicked) {
					return { code: ToolCallResultCode.success, name: toolName, result: toolResultVal, error: '', content };
				}
			}
			this.chatThreadService.setStreamState(containerId, threadId, { isStreaming: true });
			toolResultVal = await ideTool(toolCall.params as any);
			content = this.toolsService.toolResultToString[toolName](toolResultVal as any);
			return { code: ToolCallResultCode.success, name: toolName, result: toolResultVal, error: '', content };
		} catch (error) {
			return { code: ToolCallResultCode.failure, name: toolName, result: null, error: error.message, content };
		}
	}
}
